import pandas as pd
import json
import re
import html

def generate_html_file():
    """
    Reads data from CSV and JSON files, processes it, and generates a single
    interactive HTML file for querying teacher information.
    """
    print("开始加载数据文件...")
    try:
        # 1. 加载数据
        gpa_df = pd.read_json('gpa.json', orient='index').reset_index().rename(columns={'index': 'name', 0: 'courses'})
        teachers_df = pd.read_csv('teachers.csv')
        comments_df = pd.read_csv('comment_保卫处.csv')
        print("数据文件加载成功。")
    except FileNotFoundError as e:
        print(f"错误: 找不到文件 {e.filename}。请确保所有必需的数据文件都在脚本所在的目录中。")
        return
    except Exception as e:
        print(f"加载数据时发生错误: {e}")
        return

    print("开始数据预处理和整合...")
    # 2. 数据预处理
    
    # 清理gpa.json中的教师姓名（例如 "姓名（别名）" -> "姓名"）
    def clean_gpa_name(name):
        return re.sub(r'（.*?）|\(.*?\)|[ \t]', '', name)

    gpa_df['clean_name'] = gpa_df['name'].apply(clean_gpa_name)

    # 3. 整合数据
    all_teachers_data = []
    
    # 以 teachers.csv 为主表进行遍历
    for _, teacher_row in teachers_df.iterrows():
        teacher_name = teacher_row['姓名']
        
        # 准备课程数据
        teacher_courses = []
        # 在 gpa_df 中查找匹配的教师
        gpa_match = gpa_df[gpa_df['clean_name'] == clean_gpa_name(teacher_name)]
        if not gpa_match.empty:
            # 展平课程列表
            courses_list = [course for sublist in gpa_match['courses'].tolist() for course in sublist]
            for course_data in courses_list:
                try:
                    teacher_courses.append({
                        'course_name': course_data[0],
                        'avg_gpa': float(course_data[1]),
                        'student_count': course_data[2],
                        'bad_rate': float(course_data[3])
                    })
                except (ValueError, IndexError):
                    continue # 如果课程数据格式不正确，则跳过
            # 按绩点降序排序
            teacher_courses.sort(key=lambda x: x['avg_gpa'], reverse=True)

        # 准备评论数据
        teacher_comments = []
        comments_match = comments_df[comments_df['老师姓名'] == teacher_name]
        if not comments_match.empty:
            for _, comment_row in comments_match.iterrows():
                teacher_comments.append({
                    'time': comment_row['发表时间'],
                    'net_likes': int(comment_row['点赞减去点踩数量']),
                    'likes': int(comment_row['点赞量']),
                    'dislikes': int(comment_row['点踩量']),
                    'content': str(comment_row['内容']).replace('\n', '<br>')
                })
            # 按“顶”数降序排序
            teacher_comments.sort(key=lambda x: x['net_likes'], reverse=True)
            
        all_teachers_data.append({
            'id': int(teacher_row['id']),
            'name': teacher_name,
            'college': teacher_row['学院'],
            'popularity': int(teacher_row['热度']),
            'rating_count': int(teacher_row['评分人数']),
            'rating': float(teacher_row['评分']),
            'pinyin': teacher_row['拼音'],
            'pinyin_short': teacher_row['拼音缩写'],
            'courses': teacher_courses,
            'comments': teacher_comments
        })

    print(f"数据处理完成，共整合了 {len(all_teachers_data)} 位教师的信息。")
    
    # 4. 将数据转换为JSON字符串以便嵌入HTML
    # 使用 html.escape 防止JSON中的特殊字符破坏JS字符串
    teachers_json = json.dumps(all_teachers_data, ensure_ascii=False)
    
    print("开始生成HTML文件...")
    # 5. 定义HTML模板、CSS和JavaScript
    html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师信息查询系统</title>
    <style>
        :root {{
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --background-color: #f8f9fa;
            --surface-color: #ffffff;
            --text-color: #212529;
            --border-color: #dee2e6;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }}
        body {{
            font-family: var(--font-family);
            margin: 0;
            background-color: var(--background-color);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            height: 100vh;
        }}
        .header {{
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 2rem;
            text-align: center;
            box-shadow: 0 2px 4px var(--shadow-color);
            z-index: 1000;
        }}
        .header h1 {{
            margin: 0;
            font-size: 1.8rem;
        }}
        .main-container {{
            display: flex;
            flex: 1;
            overflow: hidden;
        }}
        .sidebar {{
            width: 350px;
            background-color: var(--surface-color);
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            transition: width 0.3s ease;
        }}
        .search-container {{
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            background: var(--surface-color);
        }}
        #search-input {{
            width: 100%;
            padding: 0.75rem;
            font-size: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-sizing: border-box;
        }}
        #teacher-list {{
            list-style-type: none;
            padding: 0;
            margin: 0;
            overflow-y: auto;
            flex: 1;
        }}
        .teacher-item {{
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.2s;
        }}
        .teacher-item:hover {{
            background-color: var(--background-color);
        }}
        .teacher-item.active {{
            background-color: #e0efff;
            border-right: 4px solid var(--primary-color);
            font-weight: bold;
        }}
        .teacher-name {{
            font-size: 1.1rem;
            color: var(--primary-color);
        }}
        .teacher-college {{
            font-size: 0.9rem;
            color: var(--secondary-color);
            margin-top: 4px;
        }}
        .details-panel {{
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
        }}
        .placeholder, .details-content {{
            max-width: 900px;
            margin: auto;
        }}
        .placeholder {{
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: var(--secondary-color);
            font-size: 1.2rem;
        }}
        .card {{
            background-color: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px var(--shadow-color);
        }}
        .details-header {{
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
        }}
        .details-header h2 {{
            margin: 0;
            font-size: 2rem;
            color: var(--text-color);
        }}
        .details-header .college {{
            font-size: 1.2rem;
            color: var(--secondary-color);
            margin-top: 0.5rem;
        }}
        .stats {{
            display: flex;
            gap: 2rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }}
        .stat-item {{
            text-align: center;
        }}
        .stat-value {{
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }}
        .stat-label {{
            font-size: 0.9rem;
            color: var(--secondary-color);
        }}
        h3 {{
            font-size: 1.5rem;
            color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.5rem;
            margin-top: 2rem;
            margin-bottom: 1.5rem;
        }}
        .course-table {{
            width: 100%;
            border-collapse: collapse;
        }}
        .course-table th, .course-table td {{
            padding: 0.8rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }}
        .course-table th {{
            background-color: var(--background-color);
        }}
        .comment {{
            border: 1px solid var(--border-color);
            border-left: 4px solid var(--secondary-color);
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0 8px 8px 0;
        }}
        .comment.hot {{
             border-left-color: #dc3545;
        }}
        .comment-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.8rem;
            color: var(--secondary-color);
            font-size: 0.9rem;
        }}
        .comment-content {{
            line-height: 1.6;
        }}
        .comment-likes {{
            font-weight: bold;
        }}
        .like {{ color: #28a745; }}
        .dislike {{ color: #dc3545; }}

        @media (max-width: 768px) {{
            .main-container {{
                flex-direction: column;
            }}
            .sidebar {{
                width: 100%;
                height: 40vh;
                border-right: none;
                border-bottom: 1px solid var(--border-color);
            }}
            .details-panel {{
                padding: 1rem;
            }}
        }}
    </style>
</head>
<body>
    <header class="header">
        <h1>浙江大学教师信息查询</h1>
    </header>
    <div class="main-container">
        <aside class="sidebar">
            <div class="search-container">
                <input type="text" id="search-input" placeholder="搜索教师姓名 / 拼音 / 缩写...">
            </div>
            <ul id="teacher-list"></ul>
        </aside>
        <main class="details-panel" id="details-panel">
            <div class="placeholder" id="placeholder">
                <p>请从左侧选择一位老师查看详情</p>
            </div>
            <div class="details-content" id="details-content" style="display: none;">
            </div>
        </main>
    </div>

    <script>
        const teachersData = {teachers_json};

        const searchInput = document.getElementById('search-input');
        const teacherList = document.getElementById('teacher-list');
        const detailsPanel = document.getElementById('details-panel');
        const placeholder = document.getElementById('placeholder');
        const detailsContent = document.getElementById('details-content');

        function renderTeacherList(filter = '') {{
            teacherList.innerHTML = '';
            const lowerCaseFilter = filter.toLowerCase();
            
            const filteredTeachers = teachersData.filter(teacher =>
                teacher.name.toLowerCase().includes(lowerCaseFilter) ||
                (teacher.pinyin && teacher.pinyin.toLowerCase().includes(lowerCaseFilter)) ||
                (teacher.pinyin_short && teacher.pinyin_short.toLowerCase().includes(lowerCaseFilter))
            );

            if (filteredTeachers.length === 0) {{
                teacherList.innerHTML = `<li class="teacher-item"><div class="teacher-name">未找到结果</div></li>`;
                return;
            }}

            filteredTeachers.forEach(teacher => {{
                const li = document.createElement('li');
                li.className = 'teacher-item';
                li.dataset.teacherId = teacher.id;
                li.innerHTML = `
                    <div class="teacher-name">${{teacher.name}}</div>
                    <div class="teacher-college">${{teacher.college}}</div>
                `;
                teacherList.appendChild(li);
            }});
        }}

        function displayTeacherDetails(teacherId) {{
            const teacher = teachersData.find(t => t.id === parseInt(teacherId));
            if (!teacher) return;

            // 高亮显示选中的教师
            document.querySelectorAll('.teacher-item').forEach(item => {{
                item.classList.remove('active');
            }});
            const activeItem = document.querySelector(`.teacher-item[data-teacher-id='${{teacherId}}']`);
            if (activeItem) {{
                activeItem.classList.add('active');
            }}

            let coursesHtml = '<h3>课程GPA信息</h3>';
            if (teacher.courses && teacher.courses.length > 0) {{
                coursesHtml += `
                    <table class="course-table">
                        <thead><tr><th>课程名称</th><th>平均绩点</th><th>选课人数</th><th>绩点标准差/烂率</th></tr></thead>
                        <tbody>
                            ${{teacher.courses.map(c => `
                                <tr>
                                    <td>${{c.course_name}}</td>
                                    <td>${{c.avg_gpa.toFixed(2)}}</td>
                                    <td>${{c.student_count}}</td>
                                    <td>${{c.bad_rate.toFixed(2)}}</td>
                                </tr>
                            `).join('')}}
                        </tbody>
                    </table>`;
            }} else {{
                coursesHtml += '<p>暂无课程GPA数据。</p>';
            }}

            let commentsHtml = '<h3>学生评价</h3>';
            if (teacher.comments && teacher.comments.length > 0) {{
                commentsHtml += `
                    ${{teacher.comments.map(comment => `
                        <div class="comment ${{comment.net_likes > 10 ? 'hot' : ''}}">
                            <div class="comment-header">
                                <span class="comment-time">${{comment.time}}</span>
                                <span class="comment-likes">
                                    <span class="like">👍 ${{{' '}}+comment.likes}}</span> | 
                                    <span class="dislike">👎 ${{{' '}}+comment.dislikes}}</span>
                                </span>
                            </div>
                            <div class="comment-content">${{comment.content}}</div>
                        </div>
                    `).join('')}}`;
            }} else {{
                commentsHtml += '<p>暂无学生评价数据。</p>';
            }}

            detailsContent.innerHTML = `
                <div class="card">
                    <div class="details-header">
                        <h2>${{teacher.name}}</h2>
                        <div class="college">${{teacher.college}}</div>
                    </div>
                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-value">${{teacher.rating.toFixed(2)}}</div>
                            <div class="stat-label">综合评分</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${{teacher.rating_count}}</div>
                            <div class="stat-label">评分人数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${{teacher.popularity}}</div>
                            <div class="stat-label">热度</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    ${{coursesHtml}}
                </div>
                
                <div class="card">
                    ${{commentsHtml}}
                </div>
            `;
            placeholder.style.display = 'none';
            detailsContent.style.display = 'block';
        }}

        searchInput.addEventListener('input', (e) => {{
            renderTeacherList(e.target.value);
            placeholder.style.display = 'block';
            detailsContent.style.display = 'none';
        }});

        teacherList.addEventListener('click', (e) => {{
            const item = e.target.closest('.teacher-item');
            if (item && item.dataset.teacherId) {{
                displayTeacherDetails(item.dataset.teacherId);
            }}
        }});

        // 初始加载
        renderTeacherList();
    </script>
</body>
</html>
"""
    
    # 6. 写入文件
    try:
        with open('teacher_query.html', 'w', encoding='utf-8') as f:
            f.write(html_template)
        print("HTML文件 'teacher_query.html' 已成功生成。")
    except IOError as e:
        print(f"写入文件时发生错误: {e}")

if __name__ == '__main__':
    generate_html_file()